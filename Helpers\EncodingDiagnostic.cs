using System;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Globalization;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// أداة تشخيص مشاكل الترميز العربي
    /// </summary>
    public static class EncodingDiagnostic
    {
        /// <summary>
        /// تشخيص شامل لمشاكل الترميز
        /// </summary>
        public static async Task<string> RunFullDiagnosticAsync()
        {
            var report = new StringBuilder();
            report.AppendLine("=== تقرير تشخيص الترميز العربي ===");
            report.AppendLine($"التاريخ والوقت: {DateTime.Now:yyyy/MM/dd HH:mm:ss}");
            report.AppendLine();

            // تشخيص إعدادات النظام
            report.AppendLine("1. إعدادات النظام:");
            report.AppendLine($"   - الثقافة الحالية: {CultureInfo.CurrentCulture.Name}");
            report.AppendLine($"   - ثقافة واجهة المستخدم: {CultureInfo.CurrentUICulture.Name}");
            report.AppendLine($"   - ترميز الإدخال: {Console.InputEncoding.EncodingName}");
            report.AppendLine($"   - ترميز الإخراج: {Console.OutputEncoding.EncodingName}");
            report.AppendLine();

            // تشخيص إعدادات التطبيق
            report.AppendLine("2. إعدادات التطبيق:");
            try
            {
                var app = Application.Current;
                if (app != null)
                {
                    report.AppendLine($"   - عدد النوافذ المفتوحة: {app.Windows.Count}");

                    // فحص اتجاه النافذة الرئيسية
                    if (app.MainWindow != null)
                    {
                        report.AppendLine($"   - اتجاه النافذة الرئيسية: {app.MainWindow.FlowDirection}");
                    }
                }
                else
                {
                    report.AppendLine("   - التطبيق غير متاح");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"   - خطأ في فحص التطبيق: {ex.Message}");
            }
            report.AppendLine();

            // تشخيص قاعدة البيانات
            report.AppendLine("3. إعدادات قاعدة البيانات:");
            try
            {
                var dbDiagnostic = await Data.ArabicDatabaseHelper.DiagnoseEncodingIssuesAsync();
                report.AppendLine($"   {dbDiagnostic.Replace("\n", "\n   ")}");
            }
            catch (Exception ex)
            {
                report.AppendLine($"   - خطأ في تشخيص قاعدة البيانات: {ex.Message}");
            }
            report.AppendLine();

            // اختبار النصوص العربية
            report.AppendLine("4. اختبار النصوص العربية:");
            var testTexts = new[]
            {
                "مرحبا بك في النظام",
                "الصندوق الاجتماعي للتنمية",
                "إدارة الزيارات الميدانية",
                "تقرير المحضر والعقد"
            };

            foreach (var testText in testTexts)
            {
                var isValid = ArabicEncodingHelper.IsValidArabicText(testText);
                var status = isValid ? "✅ صحيح" : "❌ يحتاج إصلاح";
                report.AppendLine($"   - '{testText}': {status}");
            }
            report.AppendLine();

            // اختبار الخطوط
            report.AppendLine("5. اختبار الخطوط:");
            try
            {
                var fontFamilies = new[]
                {
                    "Segoe UI",
                    "Tahoma",
                    "Arial Unicode MS",
                    "Microsoft Sans Serif"
                };

                foreach (var fontName in fontFamilies)
                {
                    try
                    {
                        var font = new System.Windows.Media.FontFamily(fontName);
                        report.AppendLine($"   - {fontName}: ✅ متاح");
                    }
                    catch
                    {
                        report.AppendLine($"   - {fontName}: ❌ غير متاح");
                    }
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"   - خطأ في فحص الخطوط: {ex.Message}");
            }
            report.AppendLine();

            // التوصيات
            report.AppendLine("6. التوصيات:");
            var recommendations = GetRecommendations();
            foreach (var recommendation in recommendations)
            {
                report.AppendLine($"   - {recommendation}");
            }

            return report.ToString();
        }

        /// <summary>
        /// الحصول على التوصيات لحل مشاكل الترميز
        /// </summary>
        private static string[] GetRecommendations()
        {
            var recommendations = new System.Collections.Generic.List<string>();

            // فحص الثقافة
            if (!CultureInfo.CurrentCulture.Name.StartsWith("ar"))
            {
                recommendations.Add("تغيير ثقافة النظام إلى العربية (ar-SA)");
            }

            // فحص الترميز
            if (Console.OutputEncoding.CodePage != 65001) // UTF-8
            {
                recommendations.Add("تعيين ترميز UTF-8 كافتراضي");
            }

            // فحص التطبيق
            try
            {
                var app = Application.Current;
                if (app?.MainWindow?.FlowDirection != FlowDirection.RightToLeft)
                {
                    recommendations.Add("تعيين اتجاه النافذة الرئيسية من اليمين إلى اليسار");
                }
            }
            catch
            {
                recommendations.Add("التحقق من إعدادات التطبيق");
            }

            if (recommendations.Count == 0)
            {
                recommendations.Add("جميع الإعدادات تبدو صحيحة");
            }

            return recommendations.ToArray();
        }

        /// <summary>
        /// إصلاح تلقائي لمشاكل الترميز
        /// </summary>
        public static async Task<bool> AutoFixEncodingIssuesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء الإصلاح التلقائي لمشاكل الترميز...");

                // إعداد الترميز العربي
                ArabicEncodingHelper.SetupArabicEncoding();

                // إصلاح جميع النوافذ المفتوحة
                UIEncodingFixer.FixAllOpenWindows();

                // إعداد قاعدة البيانات
                await Data.ArabicDatabaseHelper.SetupArabicSupportAsync();

                System.Diagnostics.Debug.WriteLine("✅ تم الإصلاح التلقائي بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل الإصلاح التلقائي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// عرض تقرير التشخيص في نافذة
        /// </summary>
        public static async Task ShowDiagnosticReportAsync()
        {
            try
            {
                var report = await RunFullDiagnosticAsync();
                
                var window = new Window
                {
                    Title = "تقرير تشخيص الترميز العربي",
                    Width = 600,
                    Height = 500,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    FlowDirection = FlowDirection.RightToLeft
                };

                var textBox = new System.Windows.Controls.TextBox
                {
                    Text = report,
                    IsReadOnly = true,
                    VerticalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Auto,
                    HorizontalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Auto,
                    FontFamily = new System.Windows.Media.FontFamily("Consolas, Courier New"),
                    FontSize = 12,
                    Margin = new Thickness(10),
                    FlowDirection = FlowDirection.RightToLeft
                };

                window.Content = textBox;
                
                // إصلاح ترميز النافذة
                UIEncodingFixer.FixWindowEncoding(window);
                
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تقرير التشخيص: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
