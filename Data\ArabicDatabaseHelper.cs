using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// مساعد لحل مشاكل الترميز العربي في قاعدة البيانات
    /// </summary>
    public static class ArabicDatabaseHelper
    {
        /// <summary>
        /// إعداد قاعدة البيانات لدعم النصوص العربية
        /// </summary>
        public static async Task<bool> SetupArabicSupportAsync()
        {
            try
            {
                var connectionString = DatabaseConfig.GetConnectionString();
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // التحقق من إعدادات الترميز في قاعدة البيانات
                var collationQuery = @"
                    SELECT DATABASEPROPERTYEX(DB_NAME(), 'Collation') AS DatabaseCollation;
                ";

                using var command = new SqlCommand(collationQuery, connection);
                var collation = await command.ExecuteScalarAsync() as string;
                
                System.Diagnostics.Debug.WriteLine($"🔍 ترميز قاعدة البيانات الحالي: {collation}");

                // إذا لم يكن الترميز يدعم العربية، نحاول تغييره
                if (!string.IsNullOrEmpty(collation) && !collation.Contains("Arabic"))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ قاعدة البيانات لا تدعم الترميز العربي بشكل مثالي");
                    
                    // محاولة تحديث إعدادات الاتصال
                    await SetConnectionEncodingAsync(connection);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد الدعم العربي لقاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعداد ترميز الاتصال
        /// </summary>
        private static async Task SetConnectionEncodingAsync(SqlConnection connection)
        {
            try
            {
                // تعيين ترميز UTF-8 للاتصال
                var encodingCommand = new SqlCommand("SET NAMES 'UTF8'", connection);
                await encodingCommand.ExecuteNonQueryAsync();
                
                System.Diagnostics.Debug.WriteLine("✅ تم تعيين ترميز UTF-8 للاتصال");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم تعيين ترميز الاتصال: {ex.Message}");
            }
        }

        /// <summary>
        /// تحويل النص إلى تنسيق آمن لقاعدة البيانات
        /// </summary>
        public static string SafeArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            try
            {
                // تحويل النص إلى UTF-8 ثم إلى Unicode
                var utf8Bytes = Encoding.UTF8.GetBytes(text);
                var unicodeText = Encoding.UTF8.GetString(utf8Bytes);
                
                return unicodeText;
            }
            catch
            {
                return text;
            }
        }

        /// <summary>
        /// استرجاع النص العربي من قاعدة البيانات بشكل صحيح
        /// </summary>
        public static string RetrieveArabicText(object dbValue)
        {
            if (dbValue == null || dbValue == DBNull.Value)
                return string.Empty;

            try
            {
                var text = dbValue.ToString();
                
                // التحقق من وجود مشاكل في الترميز
                if (text.Contains("?") || text.Contains("�"))
                {
                    // محاولة إصلاح الترميز
                    return Helpers.ArabicEncodingHelper.FixArabicText(text);
                }
                
                return text;
            }
            catch
            {
                return dbValue?.ToString() ?? string.Empty;
            }
        }

        /// <summary>
        /// إنشاء معامل SQL آمن للنص العربي
        /// </summary>
        public static SqlParameter CreateArabicParameter(string parameterName, string value)
        {
            var parameter = new SqlParameter(parameterName, SqlDbType.NVarChar)
            {
                Value = string.IsNullOrEmpty(value) ? (object)DBNull.Value : SafeArabicText(value)
            };
            
            return parameter;
        }

        /// <summary>
        /// تشخيص مشاكل الترميز في قاعدة البيانات
        /// </summary>
        public static async Task<string> DiagnoseEncodingIssuesAsync()
        {
            try
            {
                var connectionString = DatabaseConfig.GetConnectionString();
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                var diagnosticQuery = @"
                    SELECT 
                        DATABASEPROPERTYEX(DB_NAME(), 'Collation') AS DatabaseCollation,
                        SERVERPROPERTY('Collation') AS ServerCollation,
                        @@VERSION AS ServerVersion
                ";

                using var command = new SqlCommand(diagnosticQuery, connection);
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var dbCollation = reader["DatabaseCollation"]?.ToString();
                    var serverCollation = reader["ServerCollation"]?.ToString();
                    var serverVersion = reader["ServerVersion"]?.ToString();

                    var report = $@"
تشخيص إعدادات قاعدة البيانات:
- ترميز قاعدة البيانات: {dbCollation}
- ترميز الخادم: {serverCollation}
- إصدار الخادم: {serverVersion}

التوصيات:
{(dbCollation?.Contains("Arabic") == true ? "✅ قاعدة البيانات تدعم العربية" : "⚠️ قد تحتاج لتغيير ترميز قاعدة البيانات")}
{(serverCollation?.Contains("Arabic") == true ? "✅ الخادم يدعم العربية" : "⚠️ قد تحتاج لتغيير ترميز الخادم")}
                    ";

                    return report.Trim();
                }

                return "❌ لم يتم الحصول على معلومات التشخيص";
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في التشخيص: {ex.Message}";
            }
        }
    }
}
