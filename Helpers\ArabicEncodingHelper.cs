using System;
using System.Text;
using System.Globalization;
using System.Threading;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد لإعداد الترميز العربي وحل مشاكل عرض النصوص العربية
    /// </summary>
    public static class ArabicEncodingHelper
    {
        /// <summary>
        /// إعداد الترميز العربي الشامل للتطبيق
        /// </summary>
        public static void SetupArabicEncoding()
        {
            try
            {
                // تسجيل مزودي الترميز
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                
                // إعداد ترميز UTF-8 كافتراضي
                Console.OutputEncoding = Encoding.UTF8;
                Console.InputEncoding = Encoding.UTF8;
                
                // إعداد الثقافة العربية
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
                
                System.Diagnostics.Debug.WriteLine("✅ تم إعداد الترميز العربي بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد الترميز العربي: {ex.Message}");
            }
        }
        
        /// <summary>
        /// تحويل النص إلى UTF-8 إذا كان يحتوي على رموز غريبة
        /// </summary>
        public static string FixArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;
                
            try
            {
                // التحقق من وجود رموز غريبة
                if (text.Contains("?") || text.Contains("�") || text.Contains("???"))
                {
                    // محاولة إصلاح الترميز
                    var bytes = Encoding.Default.GetBytes(text);
                    var fixedText = Encoding.UTF8.GetString(bytes);
                    
                    // إذا لم يتم الإصلاح، إرجاع النص الأصلي
                    return string.IsNullOrEmpty(fixedText) ? text : fixedText;
                }
                
                return text;
            }
            catch
            {
                return text;
            }
        }
        
        /// <summary>
        /// التحقق من صحة النص العربي
        /// </summary>
        public static bool IsValidArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return true;
                
            // التحقق من عدم وجود رموز غريبة
            return !text.Contains("?") && !text.Contains("�") && !text.Contains("???");
        }
        
        /// <summary>
        /// تنظيف النص من الرموز الغريبة
        /// </summary>
        public static string CleanArabicText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;
                
            try
            {
                // إزالة الرموز الغريبة
                text = text.Replace("?", "").Replace("�", "").Replace("???", "");
                
                // تنظيف المسافات الزائدة
                text = text.Trim();
                
                return text;
            }
            catch
            {
                return text;
            }
        }
        
        /// <summary>
        /// إعداد خصائص النص العربي للعنصر
        /// </summary>
        public static void SetupArabicTextProperties(System.Windows.FrameworkElement element)
        {
            try
            {
                if (element != null)
                {
                    element.FlowDirection = System.Windows.FlowDirection.RightToLeft;
                    System.Windows.Media.TextOptions.SetTextFormattingMode(element, System.Windows.Media.TextFormattingMode.Display);
                    System.Windows.Media.TextOptions.SetTextRenderingMode(element, System.Windows.Media.TextRenderingMode.ClearType);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد خصائص النص العربي: {ex.Message}");
            }
        }
    }
}
