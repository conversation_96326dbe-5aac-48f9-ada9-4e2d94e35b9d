@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    🚀 تشغيل نظام إدارة الزيارات الميدانية
echo    (مع إصلاح الترميز العربي)
echo ========================================
echo.
echo 📋 جاري تشغيل النظام مع إعدادات الترميز العربي...
echo.

cd /d "%~dp0"

REM إعداد متغيرات البيئة للترميز العربي
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
set LC_ALL=ar_SA.UTF-8
set LANG=ar_SA.UTF-8

echo ✅ تم إعداد متغيرات الترميز العربي
echo 🚀 جاري تشغيل النظام...
echo.

dotnet run --configuration Release

echo.
echo ⏸️  تم إغلاق النظام
echo.
echo ملاحظة: إذا ظهرت رموز غريبة بدلاً من النصوص العربية،
echo يرجى إعادة تشغيل النظام أو الاتصال بالدعم الفني.
pause
