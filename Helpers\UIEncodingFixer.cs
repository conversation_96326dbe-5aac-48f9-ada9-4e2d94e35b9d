using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Collections.Generic;
using System.Linq;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد لإصلاح مشاكل الترميز في واجهة المستخدم
    /// </summary>
    public static class UIEncodingFixer
    {
        /// <summary>
        /// إصلاح الترميز لجميع العناصر في النافذة
        /// </summary>
        public static void FixWindowEncoding(Window window)
        {
            if (window == null) return;

            try
            {
                // إعداد خصائص النافذة
                window.FlowDirection = FlowDirection.RightToLeft;
                SetTextProperties(window);

                // إصلاح جميع العناصر الفرعية
                FixChildrenEncoding(window);

                System.Diagnostics.Debug.WriteLine($"✅ تم إصلاح ترميز النافذة: {window.GetType().Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح ترميز النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح الترميز لعنصر محدد وجميع عناصره الفرعية
        /// </summary>
        public static void FixControlEncoding(FrameworkElement element)
        {
            if (element == null) return;

            try
            {
                // إعداد خصائص العنصر
                element.FlowDirection = FlowDirection.RightToLeft;
                SetTextProperties(element);

                // إصلاح النص إذا كان العنصر يحتوي على نص
                FixElementText(element);

                // إصلاح العناصر الفرعية
                FixChildrenEncoding(element);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح ترميز العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح النص في عنصر محدد
        /// </summary>
        private static void FixElementText(FrameworkElement element)
        {
            try
            {
                switch (element)
                {
                    case TextBlock textBlock:
                        if (!string.IsNullOrEmpty(textBlock.Text))
                        {
                            textBlock.Text = ArabicEncodingHelper.FixArabicText(textBlock.Text);
                        }
                        break;

                    case TextBox textBox:
                        if (!string.IsNullOrEmpty(textBox.Text))
                        {
                            textBox.Text = ArabicEncodingHelper.FixArabicText(textBox.Text);
                        }
                        break;

                    case Button button:
                        if (button.Content is string buttonText && !string.IsNullOrEmpty(buttonText))
                        {
                            button.Content = ArabicEncodingHelper.FixArabicText(buttonText);
                        }
                        break;

                    case Label label:
                        if (label.Content is string labelText && !string.IsNullOrEmpty(labelText))
                        {
                            label.Content = ArabicEncodingHelper.FixArabicText(labelText);
                        }
                        break;

                    case ComboBox comboBox:
                        FixComboBoxItems(comboBox);
                        break;

                    case DataGrid dataGrid:
                        FixDataGridText(dataGrid);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح نص العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح نصوص عناصر ComboBox
        /// </summary>
        private static void FixComboBoxItems(ComboBox comboBox)
        {
            try
            {
                for (int i = 0; i < comboBox.Items.Count; i++)
                {
                    if (comboBox.Items[i] is string itemText)
                    {
                        comboBox.Items[i] = ArabicEncodingHelper.FixArabicText(itemText);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح عناصر ComboBox: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح نصوص DataGrid
        /// </summary>
        private static void FixDataGridText(DataGrid dataGrid)
        {
            try
            {
                // إصلاح عناوين الأعمدة
                foreach (var column in dataGrid.Columns)
                {
                    if (!string.IsNullOrEmpty(column.Header?.ToString()))
                    {
                        column.Header = ArabicEncodingHelper.FixArabicText(column.Header.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح DataGrid: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح العناصر الفرعية
        /// </summary>
        private static void FixChildrenEncoding(DependencyObject parent)
        {
            try
            {
                var children = GetChildren(parent);
                foreach (var child in children)
                {
                    if (child is FrameworkElement element)
                    {
                        FixControlEncoding(element);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح العناصر الفرعية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على جميع العناصر الفرعية
        /// </summary>
        private static IEnumerable<DependencyObject> GetChildren(DependencyObject parent)
        {
            var children = new List<DependencyObject>();

            try
            {
                int childCount = VisualTreeHelper.GetChildrenCount(parent);
                for (int i = 0; i < childCount; i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    children.Add(child);
                    children.AddRange(GetChildren(child));
                }
            }
            catch
            {
                // في حالة فشل VisualTreeHelper، نحاول LogicalTreeHelper
                try
                {
                    foreach (var child in LogicalTreeHelper.GetChildren(parent).OfType<DependencyObject>())
                    {
                        children.Add(child);
                        children.AddRange(GetChildren(child));
                    }
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }

            return children;
        }

        /// <summary>
        /// إعداد خصائص النص للعنصر
        /// </summary>
        private static void SetTextProperties(FrameworkElement element)
        {
            try
            {
                TextOptions.SetTextFormattingMode(element, TextFormattingMode.Display);
                TextOptions.SetTextRenderingMode(element, TextRenderingMode.ClearType);
                TextOptions.SetTextHintingMode(element, TextHintingMode.Fixed);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد خصائص النص: {ex.Message}");
            }
        }

        /// <summary>
        /// إصلاح ترميز جميع النوافذ المفتوحة
        /// </summary>
        public static void FixAllOpenWindows()
        {
            try
            {
                foreach (Window window in Application.Current.Windows)
                {
                    FixWindowEncoding(window);
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إصلاح ترميز جميع النوافذ المفتوحة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح النوافذ المفتوحة: {ex.Message}");
            }
        }
    }
}
